from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import requests
import uvicorn
import os

app = FastAPI(title="AJAIA CRM Intelligence", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variable to store the bearer token
bearer_token = None


# Pydantic models for request/response validation
class AuthRequest(BaseModel):
    username: str
    apikey: str


class SearchRequest(BaseModel):
    query: str


class ApiResponse(BaseModel):
    success: bool
    error: str = None
    data: dict = None


# Serve static files (if you have any CSS/JS files)
# app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def index():
    """Serve the main HTML page"""
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="HTML file not found")


@app.post("/api/authenticate")
async def authenticate(auth_data: AuthRequest):
    """Authenticate with Preqin API"""
    global bearer_token

    if not auth_data.username or not auth_data.apikey:
        raise HTTPException(status_code=400, detail="Username and API key required")

    try:
        # Authenticate with Preqin API
        auth_payload = {
            'username': auth_data.username,
            'apikey': auth_data.apikey
        }

        response = requests.post(
            'https://api.preqin.com/connect/token',
            data=auth_payload,
            headers={'Content-Type': 'application/x-www-form-urlencoded'}
        )

        print(f"Auth response status: {response.status_code}")
        print(f"Auth response: {response.text}")

        if response.status_code == 200:
            token_data = response.json()
            bearer_token = token_data.get('access_token')
            print(f"Bearer token obtained: {bearer_token[:20]}..." if bearer_token else "No token received")
            return {"success": True}
        else:
            print(f"Auth failed with status {response.status_code}: {response.text}")
            raise HTTPException(status_code=401, detail="Authentication failed")

    except requests.RequestException as e:
        print(f"Authentication exception: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")


@app.post("/api/search")
async def search(search_data: SearchRequest):
    """Search for companies using Preqin API"""
    global bearer_token

    if not bearer_token:
        raise HTTPException(status_code=401, detail="Not authenticated")

    if not search_data.query:
        raise HTTPException(status_code=400, detail="Query required")

    print(f"Searching for: {search_data.query}")

    headers = {
        'Authorization': f'Bearer {bearer_token}',
        'Content-Type': 'application/json'
    }

    try:
        # Search Fund Managers
        print("Searching Fund Managers...")
        fm_response = requests.get(
            'https://api.preqin.com/api/fundmanager',
            headers=headers,
            params={
                'fundManagerName': search_data.query,
                'InactiveFundManagers': '2',
                'include': 'pe,pd,re,inf,nr,hf'
            }
        )

        print(f"Fund Manager response status: {fm_response.status_code}")
        print(f"Fund Manager response: {fm_response.text[:500]}...")

        fund_managers = []
        if fm_response.status_code == 200:
            fm_data = fm_response.json()
            if isinstance(fm_data, dict) and 'data' in fm_data:
                fund_managers = fm_data['data'] if isinstance(fm_data['data'], list) else [fm_data['data']]
            else:
                fund_managers = fm_data if isinstance(fm_data, list) else [fm_data]
            print(f"Found {len(fund_managers)} fund managers")

        # Search Investors
        print("Searching Investors...")
        inv_response = requests.get(
            'https://api.preqin.com/api/Investor',
            headers=headers,
            params={
                'FirmName': search_data.query,
                'InactiveInvestors': '2',
                'include': 'pe,pd,re,inf,nr,hf'
            }
        )

        print(f"Investor response status: {inv_response.status_code}")
        print(f"Investor response: {inv_response.text[:500]}...")

        investors = []
        if inv_response.status_code == 200:
            inv_data = inv_response.json()
            if isinstance(inv_data, dict) and 'data' in inv_data:
                investors = inv_data['data'] if isinstance(inv_data['data'], list) else [inv_data['data']]
            else:
                investors = inv_data if isinstance(inv_data, list) else [inv_data]
            print(f"Found {len(investors)} investors")

        # Get the first company found
        company = fund_managers[0] if fund_managers else (investors[0] if investors else None)

        if not company:
            print("No companies found!")
            raise HTTPException(status_code=404, detail="No companies found")

        print(f"Using company: {company}")

        # DEBUG: Print the entire company object
        print("=== FULL COMPANY DATA ===")
        for key, value in company.items():
            print(f"{key}: {value}")
        print("=== END COMPANY DATA ===")

        # Get firm ID for contact and address lookup
        firm_id = company.get('firmID') or company.get('FirmID') or company.get('fundManagerId')
        print(f"Firm ID: {firm_id}")

        contacts = []
        addresses = []

        if firm_id:
            # Get contacts
            print("Getting contacts...")
            try:
                contacts_response = requests.get(
                    'https://api.preqin.com/api/fundmanager/contact',
                    headers=headers,
                    params={'firmId': firm_id}
                )
                print(f"Contacts response status: {contacts_response.status_code}")
                print(f"Contacts response: {contacts_response.text[:300]}...")

                if contacts_response.status_code == 200:
                    contacts_data = contacts_response.json()
                    if isinstance(contacts_data, dict) and 'data' in contacts_data:
                        contacts = contacts_data['data'] if isinstance(contacts_data['data'], list) else [
                            contacts_data['data']]
                    else:
                        contacts = contacts_data if isinstance(contacts_data, list) else [contacts_data]
                    print(f"Found {len(contacts)} contacts")

                    if contacts:
                        print("=== FIRST CONTACT DATA ===")
                        for key, value in contacts[0].items():
                            print(f"{key}: {value}")
                        print("=== END CONTACT DATA ===")
            except Exception as e:
                print(f"Error getting fund manager contacts: {e}")
                # Try investor contacts if fund manager contacts fail
                try:
                    inv_contacts_response = requests.get(
                        'https://api.preqin.com/api/Investor/contact',
                        headers=headers,
                        params={'firmId': firm_id}
                    )
                    print(f"Investor contacts response status: {inv_contacts_response.status_code}")
                    if inv_contacts_response.status_code == 200:
                        inv_contacts_data = inv_contacts_response.json()
                        if isinstance(inv_contacts_data, dict) and 'data' in inv_contacts_data:
                            contacts = inv_contacts_data['data'] if isinstance(inv_contacts_data['data'], list) else [
                                inv_contacts_data['data']]
                        else:
                            contacts = inv_contacts_data if isinstance(inv_contacts_data, list) else [inv_contacts_data]
                        print(f"Found {len(contacts)} investor contacts")
                except Exception as e2:
                    print(f"Error getting investor contacts: {e2}")

            # Get addresses
            print("Getting addresses...")
            try:
                address_response = requests.get(
                    'https://api.preqin.com/api/fundmanager/address',
                    headers=headers,
                    params={'firmId': firm_id}
                )
                print(f"Address response status: {address_response.status_code}")
                print(f"Address response: {address_response.text[:300]}...")

                if address_response.status_code == 200:
                    address_data = address_response.json()
                    if isinstance(address_data, dict) and 'data' in address_data:
                        addresses = address_data['data'] if isinstance(address_data['data'], list) else [
                            address_data['data']]
                    else:
                        addresses = address_data if isinstance(address_data, list) else [address_data]
                    print(f"Found {len(addresses)} addresses")
            except Exception as e:
                print(f"Error getting fund manager addresses: {e}")
                try:
                    inv_address_response = requests.get(
                        'https://api.preqin.com/api/Investor/address',
                        headers=headers,
                        params={'firmId': firm_id}
                    )
                    print(f"Investor address response status: {inv_address_response.status_code}")
                    if inv_address_response.status_code == 200:
                        inv_address_data = inv_address_response.json()
                        if isinstance(inv_address_data, dict) and 'data' in inv_address_data:
                            addresses = inv_address_data['data'] if isinstance(inv_address_data['data'], list) else [
                                inv_address_data['data']]
                        else:
                            addresses = inv_address_data if isinstance(inv_address_data, list) else [inv_address_data]
                        print(f"Found {len(addresses)} investor addresses")
                except Exception as e2:
                    print(f"Error getting investor addresses: {e2}")

        # Ensure we have exactly 5 contacts
        while len(contacts) < 5:
            contacts.append({
                'contactId': len(contacts),
                'contactName': 'N/A',
                'jobTitle': 'N/A',
                'email': 'N/A',
                'tel': 'N/A',
                'city': 'N/A'
            })

        # Transform data for frontend
        transformed_data = {
            'company': {
                'name': company.get('firmName') or company.get('FirmName') or company.get(
                    'fundManagerName') or 'Unknown Company',
                'type': company.get('firmType') or company.get('Type') or company.get(
                    'InvestorType') or 'Investment Firm',
                'location': company.get('city') or company.get('Location') or 'Not specified',
                'founded': company.get('yearEst') or company.get('Founded') or company.get(
                    'YearFounded') or 'Not specified',
                'aum': company.get('aumCurrMn') or company.get('AUM') or company.get(
                    'AssetsUnderManagement') or 'Not disclosed',
                'employees': company.get('Employees') or 'Not specified',
                'description': company.get('about') or company.get('About') or company.get(
                    'Description') or 'No description available',
                'website': company.get('website') or company.get('Website'),

                # Additional fields from the API response
                'address': company.get('address'),
                'city_full': company.get('city'),
                'state': company.get('stateCounty'),
                'zipCode': company.get('zipCode'),
                'country': company.get('country'),
                'region': company.get('region'),
                'phone': company.get('tel'),
                'email': company.get('email'),
                'generalConsultant': company.get('generalConsultant'),
                'secondaryLocations': company.get('secondaryLocations'),

                # Financial metrics
                'matchingFunds': company.get('matchingFunds'),
                'aumUSDMn': company.get('aumUSDMn'),
                'aumEURMn': company.get('aumEURMn'),

                # Asset allocation data
                'allocationFixedIncomePct': company.get('allocationFixedIncomePct'),
                'allocationFixedIncomeCurrMn': company.get('allocationFixedIncomeCurrMn'),
                'allocationOtherPct': company.get('allocationOtherPct'),
                'allocationOtherCurrMn': company.get('allocationOtherCurrMn'),
            },
            'contacts': [
                {
                    'id': contact.get('contactId', i),
                    'name': contact.get('contactName') or contact.get('Name') or 'N/A',
                    'title': contact.get('jobTitle') or contact.get('Title') or contact.get('Position') or 'N/A',
                    'department': contact.get('Department') or 'N/A',
                    'email': contact.get('email') or contact.get('Email') or 'N/A',
                    'phone': contact.get('tel') or contact.get('Phone') or contact.get('PhoneNumber') or 'N/A',
                    'location': contact.get('city') or contact.get('Location') or 'N/A',
                    'firstName': contact.get('firstName') or 'N/A',
                    'lastName': contact.get('lastName') or 'N/A',
                    'titlePrefix': contact.get('title') or 'N/A',
                    'assetClass': contact.get('assetClass') or 'N/A',
                    'linkedIn': contact.get('linkedIn') or 'N/A',
                    'state': contact.get('state') or 'N/A',
                    'country': contact.get('country') or 'N/A',
                    'zipCode': contact.get('zipCode') or 'N/A',
                    'firmType': contact.get('firmType') or 'N/A',
                    'firmName': contact.get('firmName') or 'N/A',
                    'localLanguageName': contact.get('localLanguageName') or 'N/A'
                }
                for i, contact in enumerate(contacts[:5])
            ],
            'addresses': addresses,
            'account': {
                'relationship_status': 'API Data Available',
                'account_manager': 'Preqin Platform',
                'last_interaction': '2024-07-02',
                'total_investments': company.get('TotalInvestments') or 'Not disclosed',
                'active_funds': company.get('ActiveFunds') or 'Not specified',
                'investment_focus': company.get('AssetClasses') or company.get('Strategies') or ['Not specified'],
                'performance_metrics': {
                    'irr': company.get('IRR') or 'N/A',
                    'moic': company.get('MOIC') or 'N/A',
                    'dpi': company.get('DPI') or 'N/A'
                }
            }
        }

        print(f"Returning transformed data for company: {transformed_data['company']['name']}")
        return {"success": True, "data": transformed_data}

    except requests.RequestException as e:
        print(f"Search exception: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "AJAIA CRM Intelligence"}


# Run the application
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,  # Enable auto-reload for development
        log_level="info"
    )